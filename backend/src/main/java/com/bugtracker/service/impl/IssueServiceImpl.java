package com.bugtracker.service.impl;

import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import com.bugtracker.model.Label;
import com.bugtracker.model.IssueCounter;
import com.bugtracker.exception.IssueIdentifierConflictException;
import com.bugtracker.repository.IssueRepository;
import com.bugtracker.repository.UserRepository;
import com.bugtracker.repository.LabelRepository;
import com.bugtracker.repository.IssueCounterRepository;
import com.bugtracker.service.IssueService;
import com.bugtracker.service.NotificationService;
import com.bugtracker.service.ValidationService;
import com.bugtracker.service.SlaService;
import com.bugtracker.service.IssueTypeFieldConfigService;
import com.bugtracker.config.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Hibernate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.transaction.annotation.Propagation;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class IssueServiceImpl implements IssueService {

    private final IssueRepository issueRepository;
    private final UserRepository userRepository;
    private final LabelRepository labelRepository;
    private final IssueCounterRepository issueCounterRepository;
    private final NotificationService notificationService;
    private final ValidationService validationService;
    private final SlaService slaService;
    private final IssueTypeFieldConfigService fieldConfigService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    public IssueServiceImpl(IssueRepository issueRepository, UserRepository userRepository, LabelRepository labelRepository, IssueCounterRepository issueCounterRepository, NotificationService notificationService, ValidationService validationService, SlaService slaService, IssueTypeFieldConfigService fieldConfigService) {
        this.issueRepository = issueRepository;
        this.userRepository = userRepository;
        this.labelRepository = labelRepository;
        this.issueCounterRepository = issueCounterRepository;
        this.notificationService = notificationService;
        this.validationService = validationService;
        this.slaService = slaService;
        this.fieldConfigService = fieldConfigService;
    }

    @Override
    @Transactional
    public Issue createIssue(Issue issue) {
        log.debug("Creating issue with type: '{}', severity: '{}', priority: '{}', status: '{}', environment: '{}'",
                 issue.getType(), issue.getSeverity(), issue.getPriority(), issue.getStatus(), issue.getEnvironment());

        try {
            // Validate issue fields against lookup tables
            validationService.validateIssueFields(
                issue.getType(),
                issue.getSeverity(),
                issue.getPriority(),
                issue.getStatus(),
                issue.getEnvironment()
            );
        } catch (IllegalArgumentException e) {
            log.error("Issue validation failed during creation: {}", e.getMessage());
            throw e; // Re-throw to maintain existing error handling
        }

        // Validate business rules before saving
        validateIssueBusinessRules(issue);

        // Generate and save issue with retry logic for constraint violations
        Issue savedIssue = createIssueWithRetry(issue);

        // Automatically add the reporter as a watcher
        if (savedIssue.getReporter() != null) {
            try {
                savedIssue.getWatchers().add(savedIssue.getReporter());
                savedIssue = issueRepository.save(savedIssue);
                log.debug("Automatically added reporter {} as watcher for issue {}",
                         savedIssue.getReporter().getUsername(), savedIssue.getIdentifier());
            } catch (Exception e) {
                log.warn("Failed to add reporter as watcher for issue {}: {}",
                        savedIssue.getIdentifier(), e.getMessage());
            }
        }

        // Create SLA tracking for the issue
        try {
            slaService.createSlaTracking(savedIssue);
            log.debug("SLA tracking created for issue: {}", savedIssue.getIdentifier());
        } catch (Exception e) {
            log.warn("Failed to create SLA tracking for issue: {} - {}", savedIssue.getIdentifier(), e.getMessage());
        }

        // Notify the assignee if assigned
        if (savedIssue.getAssignee() != null) {
            notificationService.createIssueAssignedNotification(savedIssue, savedIssue.getAssignee());
        }

        log.debug("Issue created successfully with ID: {} and identifier: {}", savedIssue.getId(), savedIssue.getIdentifier());
        return savedIssue;
    }

    /**
     * Create issue with retry logic to handle duplicate identifier constraint violations
     * This method implements exponential backoff retry strategy for production-grade reliability
     */
    private Issue createIssueWithRetry(Issue issue) {
        int maxRetries = 5;
        int baseDelayMs = 100;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Generate a unique identifier if not already set
                if (issue.getIdentifier() == null || attempt > 1) {
                    String newIdentifier = generateUniqueIssueIdentifier(issue);
                    issue.setIdentifier(newIdentifier);
                    log.debug("Generated identifier '{}' for issue (attempt {})", newIdentifier, attempt);
                }

                // Verify identifier uniqueness before saving
                if (issueRepository.findByIdentifier(issue.getIdentifier()).isPresent()) {
                    log.warn("Identifier '{}' already exists, retrying with new identifier (attempt {})",
                            issue.getIdentifier(), attempt);
                    issue.setIdentifier(null); // Force regeneration
                    continue;
                }

                // Save the issue
                Issue savedIssue = issueRepository.save(issue);
                log.debug("Issue saved successfully with identifier: {}", savedIssue.getIdentifier());
                return savedIssue;

            } catch (Exception e) {
                // Check if it's a constraint violation on the identifier
                if (isIdentifierConstraintViolation(e)) {
                    log.warn("Duplicate identifier constraint violation on attempt {} for identifier '{}': {}",
                            attempt, issue.getIdentifier(), e.getMessage());

                    if (attempt < maxRetries) {
                        // Reset identifier to force regeneration
                        issue.setIdentifier(null);

                        // Exponential backoff with jitter
                        int delayMs = baseDelayMs * (int) Math.pow(2, attempt - 1);
                        int jitter = (int) (Math.random() * delayMs * 0.1); // 10% jitter

                        try {
                            Thread.sleep(delayMs + jitter);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("Issue creation interrupted", ie);
                        }

                        log.debug("Retrying issue creation after {}ms delay (attempt {})", delayMs + jitter, attempt + 1);
                        continue;
                    } else {
                        log.error("Failed to create issue after {} attempts due to identifier conflicts", maxRetries);
                        throw new IssueIdentifierConflictException(
                            "Unable to generate unique issue identifier after " + maxRetries + " attempts. Please try again.",
                            issue.getType(),
                            issue.getIdentifier(),
                            maxRetries,
                            e
                        );
                    }
                } else {
                    // Non-constraint violation error, don't retry
                    log.error("Non-recoverable error during issue creation: {}", e.getMessage());
                    throw new RuntimeException("Failed to create issue: " + e.getMessage(), e);
                }
            }
        }

        throw new RuntimeException("Failed to create issue after " + maxRetries + " attempts");
    }

    /**
     * Check if the exception is related to identifier constraint violation
     */
    private boolean isIdentifierConstraintViolation(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        // Check for various database constraint violation patterns
        return message.contains("uk3tytaluobgb0byoipk88tbxlr") || // Hibernate generated constraint name
               message.contains("duplicate key") ||
               message.contains("unique constraint") ||
               message.contains("identifier") ||
               (message.contains("constraint") && message.contains("violation"));
    }

    @Override
    public Optional<Issue> getIssueById(Long id) {
        return issueRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Issue> getIssueByIdWithDetails(Long id) {
        Optional<Issue> issueOpt = issueRepository.findById(id);
        if (issueOpt.isPresent()) {
            Issue issue = issueOpt.get();
            // Properly initialize all lazy collections within the transaction context
            // This ensures all collections are loaded and available for serialization
            try {
                issue.getComments().size();
                issue.getAttachments().size();
                issue.getChecklistItems().size();
                issue.getLabels().size();
                issue.getWatchers().size();
                issue.getChildren().size();

                // Also initialize nested collections if needed
                if (issue.getParent() != null) {
                    issue.getParent().getIdentifier(); // Ensure parent is loaded
                }

                log.debug("Successfully loaded issue {} with all collections", issue.getIdentifier());
            } catch (Exception e) {
                log.warn("Error loading some collections for issue {}: {}", issue.getIdentifier(), e.getMessage());
                // Continue with partial loading - better than failing completely
            }

            return Optional.of(issue);
        }
        return issueOpt;
    }

    @Override
    public Optional<Issue> getIssueByIdentifier(String identifier) {
        return issueRepository.findByIdentifier(identifier);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Issue> getIssueByIdentifierWithDetails(String identifier) {
        Optional<Issue> issueOpt = issueRepository.findByIdentifier(identifier);
        if (issueOpt.isPresent()) {
            Issue issue = issueOpt.get();
            // Simple approach: just trigger lazy loading by accessing the collections
            // This will load them within the transaction context
            issue.getComments().size();
            issue.getAttachments().size();
            issue.getChecklistItems().size();
            // Skip the problematic collections that cause ConcurrentModificationException
            // issue.getLabels().size();
            // issue.getWatchers().size();
            // issue.getChildren().size();

            return Optional.of(issue);
        }
        return issueOpt;
    }

    @Override
    public Page<Issue> getAllIssues(Pageable pageable) {
        // Use tenant-aware query for getting all issues
        List<Issue> allIssues = getTenantAwareIssues();
        return convertListToPage(allIssues, pageable);
    }

    @Override
    public Page<Issue> getIssuesByAssignee(User assignee, Pageable pageable) {
        return issueRepository.findByAssignee(assignee, pageable);
    }

    @Override
    public Page<Issue> getIssuesByReporter(User reporter, Pageable pageable) {
        return issueRepository.findByReporter(reporter, pageable);
    }



    @Override
    public List<Issue> getOverdueIssues() {
        // Use tenant-aware filtering for overdue issues to ensure consistency with dashboard stats
        List<Issue> allIssues = getTenantAwareIssues();
        LocalDateTime now = LocalDateTime.now();

        return allIssues.stream()
                .filter(issue -> issue.getDevCompletionDate() != null
                        && issue.getDevCompletionDate().isBefore(now)
                        && !"RESOLVED".equals(issue.getStatus())
                        && !"CLOSED".equals(issue.getStatus()))
                .toList();
    }

    @Override
    public List<Issue> getActiveIssuesByAssignee(User assignee) {
        return issueRepository.findActiveIssuesByAssignee(assignee);
    }

    @Override
    public List<Issue> getChildIssues(Issue parent) {
        return issueRepository.findChildIssues(parent);
    }

    @Override
    public Page<Issue> getRootIssues(Pageable pageable) {
        return issueRepository.findRootIssues(pageable);
    }

    @Override
    public Page<Issue> searchIssues(String keyword, Pageable pageable) {
        return issueRepository.searchIssues(keyword, pageable);
    }

    @Override
    public Issue updateIssue(Issue issue) {
        // Validate issue fields against lookup tables
        validationService.validateIssueFields(
            issue.getType(),
            issue.getSeverity(),
            issue.getPriority(),
            issue.getStatus(),
            issue.getEnvironment()
        );

        Issue existingIssue = issueRepository.findById(issue.getId())
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issue.getId()));

        User previousAssignee = existingIssue.getAssignee();
        User newAssignee = issue.getAssignee();

        // Validate business rules before saving
        validateIssueBusinessRules(issue);

        // Handle status changes
        if (!existingIssue.getStatus().equals(issue.getStatus())) {
            // If issue is being closed, set completion date
            if ("CLOSED".equals(issue.getStatus())) {
                issue.setCompletionDate(LocalDateTime.now());
                // Mark SLA resolution as completed
                try {
                    slaService.markResolutionCompleted(issue.getId());
                } catch (Exception e) {
                    log.warn("Failed to mark SLA resolution completed for issue: {} - {}", issue.getIdentifier(), e.getMessage());
                }
            }

            // If issue is being resolved, mark SLA resolution as completed
            if ("RESOLVED".equals(issue.getStatus())) {
                try {
                    slaService.markResolutionCompleted(issue.getId());
                } catch (Exception e) {
                    log.warn("Failed to mark SLA resolution completed for issue: {} - {}", issue.getIdentifier(), e.getMessage());
                }
            }

            // If issue is being reopened from CLOSED or RESOLVED status
            if (("CLOSED".equals(existingIssue.getStatus()) ||
                 "RESOLVED".equals(existingIssue.getStatus())) &&
                "REOPENED".equals(issue.getStatus())) {
                // Increment reopen count
                issue.setReopenCount(existingIssue.getReopenCount() + 1);
                // Clear completion date when reopened
                issue.setCompletionDate(null);
            }

            // If issue is being assigned for the first time or status changes to IN_PROGRESS
            if ("IN_PROGRESS".equals(issue.getStatus()) && !"IN_PROGRESS".equals(existingIssue.getStatus())) {
                try {
                    slaService.markResponseCompleted(issue.getId());
                } catch (Exception e) {
                    log.warn("Failed to mark SLA response completed for issue: {} - {}", issue.getIdentifier(), e.getMessage());
                }
            }
        }

        Issue updatedIssue = issueRepository.save(issue);

        // Notify if assignee has changed
        if (newAssignee != null && (previousAssignee == null || !previousAssignee.getId().equals(newAssignee.getId()))) {
            notificationService.createIssueAssignedNotification(updatedIssue, newAssignee);
        }

        // Notify watchers about the update
        for (User watcher : updatedIssue.getWatchers()) {
            notificationService.createIssueUpdatedNotification(updatedIssue, watcher);
        }

        return updatedIssue;
    }

    @Override
    public void deleteIssue(Long id) {
        issueRepository.deleteById(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String generateIssueIdentifier(Issue issue) {
        // Delegate to the new method for backward compatibility
        return generateUniqueIssueIdentifier(issue);
    }

    /**
     * Generate a unique issue identifier with enhanced tenant-aware and thread-safe logic
     * This method includes additional uniqueness verification and tenant context
     */
    private String generateUniqueIssueIdentifier(Issue issue) {
        String type = issue.getType();

        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("Issue type cannot be null or empty");
        }

        // Validate that the issue type exists in lookup values
        if (!validationService.isValidLookupValue("ISSUE_TYPE", type)) {
            throw new IllegalArgumentException("Invalid issue type: " + type + ". Valid values are: " + validationService.getValidCodes("ISSUE_TYPE"));
        }

        // Generate prefix from issue type
        String prefix = generatePrefixFromType(type);

        // Get tenant context for logging and debugging
        String tenantContext = TenantContext.getCurrentTenant();
        log.debug("Generating identifier for issue type '{}' in tenant '{}'", type, tenantContext);

        // Get the next counter value for this issue type with enhanced error handling
        int nextCounter = getNextCounterForTypeWithRetry(type);

        // Format the identifier
        String identifier = String.format("%s-%03d", prefix, nextCounter);

        // Additional verification: check if identifier already exists
        // This provides an extra safety net against race conditions
        int verificationAttempts = 0;
        while (issueRepository.findByIdentifier(identifier).isPresent() && verificationAttempts < 3) {
            verificationAttempts++;
            log.warn("Generated identifier '{}' already exists, attempting to get next counter (verification attempt {})",
                    identifier, verificationAttempts);

            // Get next counter again
            nextCounter = getNextCounterForTypeWithRetry(type);
            identifier = String.format("%s-%03d", prefix, nextCounter);
        }

        if (verificationAttempts >= 3) {
            log.error("Failed to generate unique identifier after {} verification attempts for type '{}'",
                    verificationAttempts, type);
            // Fallback to timestamp-based identifier
            long timestamp = System.currentTimeMillis();
            identifier = String.format("%s-%d", prefix, timestamp % 100000);
        }

        log.debug("Generated unique identifier '{}' for issue type '{}' in tenant '{}'",
                identifier, type, tenantContext);
        return identifier;
    }

    /**
     * Generate a prefix from the issue type
     * Uses intelligent abbreviation rules for common types
     */
    private String generatePrefixFromType(String type) {
        // Handle common abbreviations
        switch (type.toUpperCase()) {
            case "BUG":
                return "BUG";
            case "FEATURE":
                return "FEAT";
            case "ENHANCEMENT":
                return "ENH";
            case "TASK":
                return "TASK";
            case "STORY":
                return "STORY";
            case "EPIC":
                return "EPIC";
            case "SPIKE":
                return "SPIKE";
            default:
                // For any new types, use the first 4-6 characters or the full type if short
                if (type.length() <= 6) {
                    return type.toUpperCase();
                } else {
                    // Take first 4 characters for longer types
                    return type.substring(0, 4).toUpperCase();
                }
        }
    }

    /**
     * Get the next counter value for the given issue type with retry logic
     * Enhanced thread-safe implementation with better error handling
     */
    private int getNextCounterForTypeWithRetry(String issueType) {
        int maxRetries = 3;
        int baseDelayMs = 50;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return getNextCounterForType(issueType);
            } catch (Exception e) {
                log.warn("Failed to get counter for type '{}' on attempt {} of {}: {}",
                        issueType, attempt, maxRetries, e.getMessage());

                if (attempt < maxRetries) {
                    // Exponential backoff
                    int delayMs = baseDelayMs * (int) Math.pow(2, attempt - 1);
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Counter generation interrupted", ie);
                    }
                } else {
                    log.error("Failed to get counter for type '{}' after {} attempts", issueType, maxRetries);
                    // Final fallback to timestamp-based counter
                    return (int) (System.currentTimeMillis() % 10000);
                }
            }
        }

        return (int) (System.currentTimeMillis() % 10000);
    }

    /**
     * Get the next counter value for the given issue type
     * Thread-safe implementation using JPA with pessimistic locking
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private int getNextCounterForType(String issueType) {
        try {
            // Use JPA with pessimistic locking for thread safety
            Optional<IssueCounter> counterOpt = issueCounterRepository.findByIssueTypeWithLock(issueType);

            if (counterOpt.isPresent()) {
                // Update existing counter
                IssueCounter counter = counterOpt.get();
                counter.setLastUsedCounter(counter.getLastUsedCounter() + 1);
                issueCounterRepository.save(counter);
                return counter.getLastUsedCounter();
            } else {
                // Create new counter
                IssueCounter newCounter = IssueCounter.builder()
                    .issueType(issueType)
                    .lastUsedCounter(1)
                    .build();
                issueCounterRepository.save(newCounter);
                return 1;
            }

        } catch (Exception e) {
            // Fallback: try to get current max and increment
            log.warn("Failed to use JPA counter increment for type {}, falling back to max+1 approach", issueType, e);
            return getFallbackCounterForType(issueType);
        }
    }

    /**
     * Fallback method to get counter when atomic increment fails
     * Uses JPA repository methods instead of native SQL
     */
    private int getFallbackCounterForType(String issueType) {
        try {
            // Get current counter or create new one
            Optional<IssueCounter> counterOpt = issueCounterRepository.findById(issueType);

            if (counterOpt.isPresent()) {
                // Update existing counter
                IssueCounter counter = counterOpt.get();
                int nextCounter = counter.getLastUsedCounter() + 1;
                counter.setLastUsedCounter(nextCounter);
                issueCounterRepository.save(counter);
                return nextCounter;
            } else {
                // Create new counter
                IssueCounter newCounter = IssueCounter.builder()
                    .issueType(issueType)
                    .lastUsedCounter(1)
                    .build();
                issueCounterRepository.save(newCounter);
                return 1;
            }
        } catch (Exception e) {
            log.error("Failed to get fallback counter for type {}", issueType, e);
            // Return a timestamp-based counter as last resort
            return (int) (System.currentTimeMillis() % 10000);
        }
    }

    @Override
    @Transactional
    public void addWatcher(Long issueId, Long userId) {
        try {
            Issue issue = issueRepository.findById(issueId)
                    .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));

            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

            // Initialize watchers collection to avoid lazy loading issues
            issue.getWatchers().size();

            // Check if user is already a watcher to avoid duplicates
            if (!issue.getWatchers().contains(user)) {
                issue.getWatchers().add(user);
                issueRepository.save(issue);
                log.debug("Added user {} as watcher for issue {}", user.getUsername(), issue.getIdentifier());
            } else {
                log.debug("User {} is already a watcher for issue {}", user.getUsername(), issue.getIdentifier());
            }
        } catch (Exception e) {
            log.error("Error adding watcher {} to issue {}: {}", userId, issueId, e.getMessage());
            throw new RuntimeException("Failed to add watcher: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void removeWatcher(Long issueId, Long userId) {
        try {
            Issue issue = issueRepository.findById(issueId)
                    .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));

            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

            // Initialize watchers collection to avoid lazy loading issues
            issue.getWatchers().size();

            if (issue.getWatchers().remove(user)) {
                issueRepository.save(issue);
                log.debug("Removed user {} as watcher from issue {}", user.getUsername(), issue.getIdentifier());
            } else {
                log.debug("User {} was not a watcher for issue {}", user.getUsername(), issue.getIdentifier());
            }
        } catch (Exception e) {
            log.error("Error removing watcher {} from issue {}: {}", userId, issueId, e.getMessage());
            throw new RuntimeException("Failed to remove watcher: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Issue> getRecentIssues(int limit) {
        // Get the most recently created issues using tenant-aware query
        return getTenantAwareIssues()
                .stream()
                .sorted((i1, i2) -> i2.getCreatedAt().compareTo(i1.getCreatedAt()))
                .limit(limit)
                .toList();
    }

    /**
     * Get all issues for the current tenant using JPA repository (database-agnostic)
     */
    private List<Issue> getTenantAwareIssues() {
        try {
            // Use JPA repository method - tenant context is handled by the multitenant configuration
            List<Issue> issues = issueRepository.findAll();

            log.debug("Found {} issues for tenant: {}", issues.size(), TenantContext.getCurrentTenant());
            return issues;

        } catch (Exception e) {
            log.error("Failed to fetch tenant-aware issues for tenant: {}", TenantContext.getCurrentTenant(), e);
            // Return empty list as fallback
            return new ArrayList<>();
        }
    }

    /**
     * Get all users for the current tenant using JPA repository (database-agnostic)
     */
    private List<User> getTenantAwareUsers() {
        try {
            // Use JPA repository method - tenant context is handled by the multitenant configuration
            List<User> users = userRepository.findAll();

            log.debug("Found {} users for tenant: {}", users.size(), TenantContext.getCurrentTenant());
            return users;

        } catch (Exception e) {
            log.error("Failed to fetch tenant-aware users for tenant: {}", TenantContext.getCurrentTenant(), e);
            // Return empty list as fallback
            return new ArrayList<>();
        }
    }

    @Override
    public Object getDashboardStats(String period) {
        return getDashboardStats(period, null);
    }

    @Override
    public Object getDashboardStats(String period, String environment) {
        // Get all issues using tenant-aware query
        List<Issue> allIssues = getTenantAwareIssues();

        log.debug("Fetched {} issues for tenant: {}", allIssues.size(), TenantContext.getContextInfo());

        // Filter issues based on environment if specified
        if (environment != null && !environment.isEmpty()) {
            allIssues = allIssues.stream()
                    .filter(issue -> issue.getEnvironment() != null &&
                            issue.getEnvironment().equals(environment))
                    .toList();
        }

        // Filter issues based on period if needed
        List<Issue> filteredIssues = allIssues;
        if (period != null && !period.isEmpty() && !period.equals("all")) {
            LocalDateTime startDate = null;
            LocalDateTime now = LocalDateTime.now();

            switch (period) {
                case "current_week":
                    // Start from the beginning of the current week
                    startDate = now.minusDays(now.getDayOfWeek().getValue() - 1).withHour(0).withMinute(0).withSecond(0);
                    break;
                case "last_week":
                    // Start from the beginning of last week
                    startDate = now.minusDays(now.getDayOfWeek().getValue() + 6).withHour(0).withMinute(0).withSecond(0);
                    break;
                case "last_2_weeks":
                    startDate = now.minusWeeks(2).withHour(0).withMinute(0).withSecond(0);
                    break;
                case "last_4_weeks":
                    startDate = now.minusWeeks(4).withHour(0).withMinute(0).withSecond(0);
                    break;
                default:
                    // Default to all issues
                    break;
            }

            if (startDate != null) {
                final LocalDateTime filterStartDate = startDate;
                filteredIssues = allIssues.stream()
                        .filter(issue -> issue.getCreatedAt() != null && issue.getCreatedAt().isAfter(filterStartDate))
                        .toList();
            }
        }

        // Calculate statistics
        int totalIssues = filteredIssues.size();

        // Handle the status mismatch between database and enum
        // In database: OPEN, in enum: NEW
        int openIssues = (int) filteredIssues.stream()
                .filter(issue -> {
                    String statusStr = issue.getStatus().toString();
                    return !statusStr.equals("RESOLVED") && !statusStr.equals("CLOSED");
                })
                .count();

        int overdueIssues = (int) filteredIssues.stream()
                .filter(issue -> issue.getDevCompletionDate() != null && issue.getDevCompletionDate().isBefore(LocalDateTime.now())
                        && !"RESOLVED".equals(issue.getStatus()) && !"CLOSED".equals(issue.getStatus()))
                .count();

        // Count issues with REOPENED status only (current reopened issues)
        // Note: reopen_count tracks historical reopens, but for dashboard we want current status
        int reopenedIssues = (int) filteredIssues.stream()
                .filter(issue -> "REOPENED".equals(issue.getStatus()))
                .count();

        // Count issues by status
        var statusCounts = new java.util.HashMap<String, Integer>();
        for (Issue issue : filteredIssues) {
            String statusStr = issue.getStatus();
            statusCounts.put(statusStr, statusCounts.getOrDefault(statusStr, 0) + 1);
        }

        // Count issues by type
        var typeCounts = new java.util.HashMap<String, Integer>();
        for (Issue issue : filteredIssues) {
            String typeStr = issue.getType();
            typeCounts.put(typeStr, typeCounts.getOrDefault(typeStr, 0) + 1);
        }

        // Calculate user workload using tenant-aware users
        var userWorkload = new java.util.ArrayList<java.util.Map<String, Object>>();
        var users = getTenantAwareUsers();

        log.debug("Fetched {} users for tenant: {}", users.size(), TenantContext.getContextInfo());
        for (User user : users) {
            var userStats = new java.util.HashMap<String, Object>();
            userStats.put("name", user.getUsername());
            userStats.put("assigned", (int) filteredIssues.stream()
                    .filter(issue -> issue.getAssignee() != null && issue.getAssignee().getId().equals(user.getId()))
                    .count());
            userStats.put("reported", (int) filteredIssues.stream()
                    .filter(issue -> issue.getReporter() != null && issue.getReporter().getId().equals(user.getId()))
                    .count());
            userWorkload.add(userStats);
        }

        // Calculate reopen counts by user - count only currently reopened issues
        var reopensByUser = new java.util.ArrayList<java.util.Map<String, Object>>();

        // Get all users who have currently reopened issues assigned to them
        var usersWithReopenedIssues = filteredIssues.stream()
                .filter(issue -> issue.getAssignee() != null && "REOPENED".equals(issue.getStatus()))
                .map(Issue::getAssignee)
                .distinct()
                .toList();

        log.debug("Found {} users with reopened issues for tenant: {}", usersWithReopenedIssues.size(), TenantContext.getContextInfo());

        for (User user : usersWithReopenedIssues) {
            // Count currently reopened issues assigned to this user
            long reopenedCount = filteredIssues.stream()
                    .filter(issue -> issue.getAssignee() != null
                            && issue.getAssignee().getId().equals(user.getId())
                            && "REOPENED".equals(issue.getStatus()))
                    .count();

            if (reopenedCount > 0) {
                var userReopen = new java.util.HashMap<String, Object>();
                userReopen.put("name", user.getUsername());
                userReopen.put("reopenCount", (int) reopenedCount);
                reopensByUser.add(userReopen);
            }
        }

        // No sample data needed - return actual data only

        // Create the dashboard stats object
        var stats = new java.util.HashMap<String, Object>();
        stats.put("totalIssues", totalIssues);
        stats.put("openIssues", openIssues);
        stats.put("overdueIssues", overdueIssues);
        stats.put("reopenedIssues", reopenedIssues);
        stats.put("statusCounts", statusCounts);
        stats.put("typeCounts", typeCounts);
        stats.put("userWorkload", userWorkload);
        stats.put("reopensByUser", reopensByUser);

        // Add myAssignedIssues for the frontend
        stats.put("myAssignedIssues", openIssues > 0 ? 1 : 0); // Just a placeholder value

        log.debug("Dashboard stats for tenant {}: totalIssues={}, reopenedIssues={}, reopensByUser.size={}",
                TenantContext.getContextInfo(), totalIssues, reopenedIssues, reopensByUser.size());

        return stats;
    }

    @Override
    @Transactional
    public void addLabel(Long issueId, Long labelId) {
        try {
            log.debug("Adding label {} to issue {} in tenant context: {}", labelId, issueId, TenantContext.getContextInfo());

            // Find issue in tenant context
            Issue issue = issueRepository.findById(issueId)
                    .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));

            // Find label in tenant context
            Label label = labelRepository.findById(labelId)
                    .orElseThrow(() -> new RuntimeException("Label not found with id: " + labelId));

            // Check if label is already added to avoid duplicates
            if (issue.getLabels().contains(label)) {
                log.debug("Label {} already exists on issue {}", labelId, issueId);
                return;
            }

            // Add label to issue
            issue.getLabels().add(label);
            issueRepository.save(issue);

            log.debug("Successfully added label {} to issue {}", labelId, issueId);
        } catch (Exception e) {
            log.error("Error adding label {} to issue {} in tenant {}: {}",
                     labelId, issueId, TenantContext.getCurrentTenant(), e.getMessage(), e);
            throw new RuntimeException("Failed to add label to issue: " + e.getMessage(), e);
        }
    }

    @Override
    public void removeLabel(Long issueId, Long labelId) {
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));

        Label label = labelRepository.findById(labelId)
                .orElseThrow(() -> new RuntimeException("Label not found with id: " + labelId));

        issue.getLabels().remove(label);
        issueRepository.save(issue);
    }

    @Override
    public Page<Issue> getFilteredIssues(String search, String status, String type, String priority, String environment, java.time.LocalDate startDate, java.time.LocalDate endDate, Pageable pageable) {
        // Get all issues first using tenant-aware query
        List<Issue> allIssuesList = getTenantAwareIssues();

        // Apply default sorting by createdAt DESC if no sorting is specified
        if (pageable.getSort().isUnsorted()) {
            allIssuesList = allIssuesList.stream()
                .sorted((i1, i2) -> i2.getCreatedAt().compareTo(i1.getCreatedAt()))
                .toList();
        }

        if (search == null && status == null && type == null && priority == null && environment == null && startDate == null && endDate == null) {
            return convertListToPage(allIssuesList, pageable);
        }

        // Convert to list for filtering
        List<Issue> filteredIssues = allIssuesList.stream()
            .filter(issue -> {
                // Search filter
                if (search != null && !search.isEmpty()) {
                    String searchLower = search.toLowerCase();
                    boolean matchesSearch = issue.getTitle().toLowerCase().contains(searchLower) ||
                                          issue.getDescription().toLowerCase().contains(searchLower) ||
                                          issue.getIdentifier().toLowerCase().contains(searchLower);
                    if (!matchesSearch) return false;
                }

                // Status filter
                if (status != null && !status.isEmpty() && !status.equals("ALL")) {
                    if (!issue.getStatus().equals(status)) return false;
                }

                // Type filter
                if (type != null && !type.isEmpty() && !type.equals("ALL")) {
                    if (!issue.getType().equals(type)) return false;
                }

                // Priority filter
                if (priority != null && !priority.isEmpty() && !priority.equals("ALL")) {
                    if (!issue.getPriority().equals(priority)) return false;
                }

                // Environment filter
                if (environment != null && !environment.isEmpty() && !environment.equals("ALL")) {
                    if (issue.getEnvironment() == null || !issue.getEnvironment().equals(environment)) {
                        return false;
                    }
                }

                // Date range filter
                if (startDate != null || endDate != null) {
                    java.time.LocalDate issueDate = issue.getCreatedAt().toLocalDate();

                    if (startDate != null && issueDate.isBefore(startDate)) {
                        return false;
                    }

                    if (endDate != null && issueDate.isAfter(endDate)) {
                        return false;
                    }
                }

                return true;
            })
            .toList();

        return convertListToPage(filteredIssues, pageable);
    }

    @Override
    public Page<Issue> convertListToPage(List<Issue> issues, Pageable pageable) {
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), issues.size());

        List<Issue> pageContent = start >= issues.size() ? new ArrayList<>() : issues.subList(start, end);

        return new PageImpl<>(pageContent, pageable, issues.size());
    }

    @Override
    @Transactional
    public Map<String, Object> bulkCreateIssues(List<Map<String, Object>> issuesData) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failedCount = 0;

        for (int i = 0; i < issuesData.size(); i++) {
            try {
                Map<String, Object> issueData = issuesData.get(i);
                Issue issue = convertMapToIssue(issueData);

                // Validate required fields
                if (issue.getTitle() == null || issue.getTitle().trim().isEmpty()) {
                    errors.add("Row " + (i + 2) + ": Title is required");
                    failedCount++;
                    continue;
                }

                if (issue.getDescription() == null || issue.getDescription().trim().isEmpty()) {
                    errors.add("Row " + (i + 2) + ": Description is required");
                    failedCount++;
                    continue;
                }

                if (issue.getEnvironment() == null) {
                    errors.add("Row " + (i + 2) + ": Environment is required");
                    failedCount++;
                    continue;
                }

                // Set default reporter if not provided
                if (issue.getReporter() == null) {
                    Optional<User> adminUser = userRepository.findByUsername("admin");
                    if (adminUser.isPresent()) {
                        issue.setReporter(adminUser.get());
                    } else {
                        List<User> users = userRepository.findAll();
                        if (!users.isEmpty()) {
                            issue.setReporter(users.get(0));
                        } else {
                            errors.add("Row " + (i + 2) + ": No users available to assign as reporter");
                            failedCount++;
                            continue;
                        }
                    }
                }

                createIssue(issue);
                successCount++;

            } catch (Exception e) {
                errors.add("Row " + (i + 2) + ": " + e.getMessage());
                failedCount++;
            }
        }

        result.put("success", successCount);
        result.put("failed", failedCount);
        result.put("errors", errors);

        return result;
    }

    private Issue convertMapToIssue(Map<String, Object> issueData) {
        Issue issue = new Issue();

        issue.setTitle((String) issueData.get("title"));
        issue.setDescription((String) issueData.get("description"));

        // Set string values directly
        if (issueData.get("type") != null) {
            issue.setType((String) issueData.get("type"));
        }

        if (issueData.get("severity") != null) {
            issue.setSeverity((String) issueData.get("severity"));
        }

        if (issueData.get("priority") != null) {
            issue.setPriority((String) issueData.get("priority"));
        }

        if (issueData.get("status") != null) {
            issue.setStatus((String) issueData.get("status"));
        } else {
            issue.setStatus("OPEN"); // Default status
        }

        if (issueData.get("environment") != null) {
            issue.setEnvironment((String) issueData.get("environment"));
        }

        // Handle assignee by username
        if (issueData.get("assigneeUsername") != null) {
            String assigneeUsername = (String) issueData.get("assigneeUsername");
            Optional<User> assignee = userRepository.findByUsername(assigneeUsername);
            if (assignee.isPresent()) {
                issue.setAssignee(assignee.get());
            }
        }

        // Handle dev completion date (legacy support for targetDate)
        if (issueData.get("devCompletionDate") != null) {
            String devCompletionDateStr = (String) issueData.get("devCompletionDate");
            try {
                issue.setDevCompletionDate(LocalDateTime.parse(devCompletionDateStr + "T00:00:00"));
            } catch (Exception e) {
                // If parsing fails, ignore the dev completion date
            }
        } else if (issueData.get("targetDate") != null) {
            // Legacy support for targetDate field
            String targetDateStr = (String) issueData.get("targetDate");
            try {
                issue.setDevCompletionDate(LocalDateTime.parse(targetDateStr + "T00:00:00"));
            } catch (Exception e) {
                // If parsing fails, ignore the target date
            }
        }

        // Handle QC completion date
        if (issueData.get("qcCompletionDate") != null) {
            String qcCompletionDateStr = (String) issueData.get("qcCompletionDate");
            try {
                issue.setQcCompletionDate(LocalDateTime.parse(qcCompletionDateStr + "T00:00:00"));
            } catch (Exception e) {
                // If parsing fails, ignore the QC completion date
            }
        }

        // Handle root cause
        if (issueData.get("rootCause") != null) {
            issue.setRootCause((String) issueData.get("rootCause"));
        }

        // Validate date logic
        if (issue.getDevCompletionDate() != null && issue.getQcCompletionDate() != null) {
            if (issue.getQcCompletionDate().isBefore(issue.getDevCompletionDate())) {
                throw new IllegalArgumentException("QC Completion Date cannot be earlier than Dev Completion Date");
            }
        }

        // Validate root cause requirement
        if ("DEV_COMPLETED".equals(issue.getStatus()) &&
            (issue.getRootCause() == null || issue.getRootCause().trim().isEmpty())) {
            throw new IllegalArgumentException("Root Cause is required when status is Dev Completed");
        }

        return issue;
    }

    /**
     * Validate business rules for issue creation and updates
     */
    private void validateIssueBusinessRules(Issue issue) {
        // Validate date logic
        if (issue.getDevCompletionDate() != null && issue.getQcCompletionDate() != null) {
            if (issue.getQcCompletionDate().isBefore(issue.getDevCompletionDate())) {
                throw new IllegalArgumentException("QC Completion Date cannot be earlier than Dev Completion Date");
            }
        }

        // Validate root cause requirement
        if ("DEV_COMPLETED".equals(issue.getStatus()) &&
            (issue.getRootCause() == null || issue.getRootCause().trim().isEmpty())) {
            throw new IllegalArgumentException("Root Cause is required when status is Dev Completed");
        }

        // Validate conditional fields based on issue type
        validateConditionalFields(issue);
    }

    /**
     * Validate conditional fields based on issue type
     */
    private void validateConditionalFields(Issue issue) {
        if (issue.getType() == null) {
            return; // Type validation is handled elsewhere
        }

        Map<String, String> fieldValues = Map.of(
            "stepsToReproduce", issue.getStepsToReproduce() != null ? issue.getStepsToReproduce() : "",
            "expectedResult", issue.getExpectedResult() != null ? issue.getExpectedResult() : "",
            "actualResult", issue.getActualResult() != null ? issue.getActualResult() : "",
            "businessJustification", issue.getBusinessJustification() != null ? issue.getBusinessJustification() : "",
            "acceptanceCriteria", issue.getAcceptanceCriteria() != null ? issue.getAcceptanceCriteria() : "",
            "userStory", issue.getUserStory() != null ? issue.getUserStory() : "",
            "definitionOfDone", issue.getDefinitionOfDone() != null ? issue.getDefinitionOfDone() : "",
            "researchNotes", issue.getResearchNotes() != null ? issue.getResearchNotes() : "",
            "technicalApproach", issue.getTechnicalApproach() != null ? issue.getTechnicalApproach() : ""
        );

        List<String> validationErrors = fieldConfigService.validateRequiredFields(issue.getType(), fieldValues);

        if (!validationErrors.isEmpty()) {
            throw new IllegalArgumentException("Validation failed: " + String.join("; ", validationErrors));
        }
    }
}
