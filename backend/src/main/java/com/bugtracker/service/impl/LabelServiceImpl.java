package com.bugtracker.service.impl;

import com.bugtracker.config.TenantContext;
import com.bugtracker.model.Label;
import com.bugtracker.repository.LabelRepository;
import com.bugtracker.service.LabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class LabelServiceImpl implements LabelService {
    
    private final LabelRepository labelRepository;
    
    @Autowired
    public LabelServiceImpl(LabelRepository labelRepository) {
        this.labelRepository = labelRepository;
    }
    
    @Override
    public List<Label> getAllLabels() {
        try {
            log.debug("Fetching all labels in tenant context: {}", TenantContext.getContextInfo());
            List<Label> labels = labelRepository.findAll();
            log.debug("Found {} labels in tenant {}", labels.size(), TenantContext.getCurrentTenant());
            return labels;
        } catch (Exception e) {
            log.error("Error fetching labels in tenant {}: {}", TenantContext.getCurrentTenant(), e.getMessage(), e);
            throw new RuntimeException("Failed to fetch labels: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Optional<Label> getLabelById(Long id) {
        return labelRepository.findById(id);
    }
    
    @Override
    public Optional<Label> getLabelByName(String name) {
        return labelRepository.findByName(name);
    }
    
    @Override
    public List<Label> searchLabelsByName(String name) {
        return labelRepository.findByNameContainingIgnoreCase(name);
    }
    
    @Override
    @Transactional
    public Label createLabel(Label label) {
        try {
            log.debug("Creating label '{}' in tenant context: {}", label.getName(), TenantContext.getContextInfo());

            // Check if label with same name already exists
            if (existsByName(label.getName())) {
                throw new RuntimeException("Label with name '" + label.getName() + "' already exists");
            }

            Label savedLabel = labelRepository.save(label);
            log.debug("Successfully created label with ID {} in tenant {}", savedLabel.getId(), TenantContext.getCurrentTenant());
            return savedLabel;
        } catch (Exception e) {
            log.error("Error creating label '{}' in tenant {}: {}",
                     label.getName(), TenantContext.getCurrentTenant(), e.getMessage(), e);
            throw new RuntimeException("Failed to create label: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Label updateLabel(Label label) {
        return labelRepository.save(label);
    }
    
    @Override
    public void deleteLabel(Long id) {
        labelRepository.deleteById(id);
    }
    
    @Override
    public boolean existsByName(String name) {
        return labelRepository.existsByName(name);
    }
}
