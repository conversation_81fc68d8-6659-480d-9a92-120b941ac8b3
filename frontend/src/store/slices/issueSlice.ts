import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import issueService from '../../services/issueService';

export interface FetchIssuesParams {
  search?: string;
  status?: string;
  type?: string;
  priority?: string;
  environment?: string;
  overdue?: boolean;
  startDate?: string | Date;
  endDate?: string | Date;
}

export const fetchIssues = createAsyncThunk(
  'issues/fetchIssues',
  async (params: FetchIssuesParams, { rejectWithValue }) => {
    try {
      return await issueService.getIssues(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch issues');
    }
  }
);

export const fetchIssueById = createAsyncThunk(
  'issues/fetchIssueById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await issueService.getIssueById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch issue');
    }
  }
);

export const createIssue = createAsyncThunk(
  'issues/createIssue',
  async (issueData: any, { rejectWithValue }) => {
    try {
      return await issueService.createIssue(issueData);
    } catch (error: any) {
      // Extract error details from the response
      const errorData = error.response?.data;
      if (errorData) {
        return rejectWithValue({
          status: errorData.status,
          message: errorData.message || 'Failed to create issue',
          path: errorData.path
        });
      }
      return rejectWithValue({
        status: error.response?.status || 500,
        message: error.message || 'Failed to create issue',
        path: '/api/issues'
      });
    }
  }
);

export const updateIssue = createAsyncThunk(
  'issues/updateIssue',
  async (issueData: any, { rejectWithValue }) => {
    try {
      // Extract the ID from the issue data
      const { id, reopenComment, targetDateChangeReason, ...updateData } = issueData;

      // Handle reopening logic
      if (updateData.status === 'REOPENED' && reopenComment) {
        console.log('Reopening issue with comment:', reopenComment);

        // Create a comment for the reopen reason
        try {
          await issueService.addComment(id, `[REOPENED] ${reopenComment}`);
        } catch (commentError) {
          console.error('Failed to add reopen comment:', commentError);
        }
      }

      // Handle target date change reason if provided
      if (targetDateChangeReason && targetDateChangeReason.trim() !== '') {
        console.log('Target date changed with reason:', targetDateChangeReason);

        // Add a comment for the target date change reason
        try {
          await issueService.addComment(id, `[TARGET DATE CHANGED] ${targetDateChangeReason}`);
        } catch (commentError) {
          console.error('Failed to add target date change comment:', commentError);
        }
      }

      // Update the issue
      return await issueService.updateIssue(id, updateData);
    } catch (error: any) {
      console.error('Error updating issue:', error);
      // Extract error details from the response
      const errorData = error.response?.data;
      if (errorData) {
        return rejectWithValue({
          status: errorData.status,
          message: errorData.message || 'Failed to update issue',
          path: errorData.path
        });
      }
      return rejectWithValue({
        status: error.response?.status || 500,
        message: error.message || 'Failed to update issue',
        path: `/api/issues/${id}`
      });
    }
  }
);

export const updateIssueStatus = createAsyncThunk(
  'issues/updateIssueStatus',
  async ({ id, status }: { id: number, status: string }, { rejectWithValue }) => {
    try {
      return await issueService.updateIssueStatus(id, status);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update issue status');
    }
  }
);

export const deleteIssue = createAsyncThunk(
  'issues/deleteIssue',
  async (id: number, { rejectWithValue }) => {
    try {
      await issueService.deleteIssue(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete issue');
    }
  }
);

export const addComment = createAsyncThunk(
  'issues/addComment',
  async ({ issueId, content }: { issueId: number, content: string }, { rejectWithValue }) => {
    try {
      return await issueService.addComment(issueId, content);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add comment');
    }
  }
);

export const deleteComment = createAsyncThunk(
  'issues/deleteComment',
  async (commentId: number, { rejectWithValue }) => {
    try {
      await issueService.deleteComment(commentId);
      return commentId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete comment');
    }
  }
);

export const addAttachment = createAsyncThunk(
  'issues/addAttachment',
  async ({ issueId, file }: { issueId: number, file: FormData }, { rejectWithValue }) => {
    try {
      return await issueService.addAttachment(issueId, file);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add attachment');
    }
  }
);

export const deleteAttachment = createAsyncThunk(
  'issues/deleteAttachment',
  async (attachmentId: number, { rejectWithValue }) => {
    try {
      await issueService.deleteAttachment(attachmentId);
      return attachmentId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete attachment');
    }
  }
);

export const addWatcher = createAsyncThunk(
  'issues/addWatcher',
  async ({ issueId, userId }: { issueId: number, userId: number }, { rejectWithValue }) => {
    try {
      await issueService.addWatcher(issueId, userId);
      return userId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add watcher');
    }
  }
);

export const removeWatcher = createAsyncThunk(
  'issues/removeWatcher',
  async ({ issueId, userId }: { issueId: number, userId: number }, { rejectWithValue }) => {
    try {
      await issueService.removeWatcher(issueId, userId);
      return userId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to remove watcher');
    }
  }
);

export const fetchDashboardStats = createAsyncThunk(
  'issues/fetchDashboardStats',
  async (params: { period?: string; environment?: string } = {}, { rejectWithValue }) => {
    try {
      return await issueService.getDashboardStats(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchRecentIssues = createAsyncThunk(
  'issues/fetchRecentIssues',
  async (_, { rejectWithValue }) => {
    try {
      return await issueService.getRecentIssues();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch recent issues');
    }
  }
);

export const fetchOverdueIssues = createAsyncThunk(
  'issues/fetchOverdueIssues',
  async (_, { rejectWithValue }) => {
    try {
      return await issueService.getOverdueIssues();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch overdue issues');
    }
  }
);

export const fetchCustomReport = createAsyncThunk(
  'issues/fetchCustomReport',
  async (params: { startDate?: string; endDate?: string; userId?: number }, { rejectWithValue }) => {
    try {
      return await issueService.getCustomReport(params);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch custom report');
    }
  }
);

export const bulkCreateIssues = createAsyncThunk(
  'issues/bulkCreateIssues',
  async (issues: any[], { rejectWithValue }) => {
    try {
      return await issueService.bulkCreateIssues(issues);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to import issues');
    }
  }
);

export const generateAIIssueDescription = createAsyncThunk(
  'issues/generateAIIssueDescription',
  async (requestData: {
    briefDescription: string;
    context?: string;
    environment?: string;
    module?: string;
    useAdvancedAI?: boolean;
  }, { rejectWithValue }) => {
    try {
      return await issueService.getAIAssistance(requestData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to generate AI assistance');
    }
  }
);

interface IssueState {
  issues: any[];
  currentIssue: any | null;
  dashboardStats: any | null;
  recentIssues: any[];
  overdueIssues: any[];
  customReport: any | null;
  aiAssistance: any | null;
  aiLoading: boolean;
  voiceAssistance: {
    isActive: boolean;
    currentStep: number;
    conversationData: any | null;
    transcript: string | null;
    isListening: boolean;
    error: string | null;
  };
  loading: boolean;
  error: string | null;
}

const initialState: IssueState = {
  issues: [],
  currentIssue: null,
  dashboardStats: null,
  recentIssues: [],
  overdueIssues: [],
  customReport: null,
  aiAssistance: null,
  aiLoading: false,
  voiceAssistance: {
    isActive: false,
    currentStep: 0,
    conversationData: null,
    transcript: null,
    isListening: false,
    error: null
  },
  loading: false,
  error: null
};

const issueSlice = createSlice({
  name: 'issues',
  initialState,
  reducers: {
    clearCurrentIssue: (state) => {
      state.currentIssue = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearAIAssistance: (state) => {
      state.aiAssistance = null;
      state.aiLoading = false;
    },
    // Voice assistance actions
    startVoiceAssistance: (state) => {
      state.voiceAssistance.isActive = true;
      state.voiceAssistance.currentStep = 0;
      state.voiceAssistance.error = null;
    },
    stopVoiceAssistance: (state) => {
      state.voiceAssistance.isActive = false;
      state.voiceAssistance.isListening = false;
      state.voiceAssistance.transcript = null;
    },
    setVoiceListening: (state, action) => {
      state.voiceAssistance.isListening = action.payload;
    },
    setVoiceTranscript: (state, action) => {
      state.voiceAssistance.transcript = action.payload;
    },
    setVoiceStep: (state, action) => {
      state.voiceAssistance.currentStep = action.payload;
    },
    setVoiceConversationData: (state, action) => {
      state.voiceAssistance.conversationData = action.payload;
    },
    setVoiceError: (state, action) => {
      state.voiceAssistance.error = action.payload;
    },
    clearVoiceAssistance: (state) => {
      state.voiceAssistance = {
        isActive: false,
        currentStep: 0,
        conversationData: null,
        transcript: null,
        isListening: false,
        error: null
      };
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchIssues.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIssues.fulfilled, (state, action) => {
        state.loading = false;
        // Ensure issues is always an array
        state.issues = Array.isArray(action.payload) ? action.payload : [];
      })
      .addCase(fetchIssues.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchIssueById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIssueById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentIssue = action.payload;
      })
      .addCase(fetchIssueById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createIssue.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createIssue.fulfilled, (state, action) => {
        state.loading = false;
        state.issues.push(action.payload);
      })
      .addCase(createIssue.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateIssue.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateIssue.fulfilled, (state, action) => {
        state.loading = false;
        state.currentIssue = action.payload;
        const index = state.issues.findIndex(issue => issue.id === action.payload.id);
        if (index !== -1) {
          state.issues[index] = action.payload;
        }
      })
      .addCase(updateIssue.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteIssue.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteIssue.fulfilled, (state, action) => {
        state.loading = false;
        state.issues = state.issues.filter(issue => issue.id !== action.payload);
        state.currentIssue = null;
      })
      .addCase(deleteIssue.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading = false;
        state.dashboardStats = action.payload;
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchRecentIssues.fulfilled, (state, action) => {
        state.recentIssues = action.payload;
      })
      .addCase(fetchOverdueIssues.fulfilled, (state, action) => {
        state.overdueIssues = action.payload;
      })
      .addCase(fetchCustomReport.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCustomReport.fulfilled, (state, action) => {
        state.loading = false;
        state.customReport = action.payload;
      })
      .addCase(fetchCustomReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(generateAIIssueDescription.pending, (state) => {
        state.aiLoading = true;
        state.error = null;
      })
      .addCase(generateAIIssueDescription.fulfilled, (state, action) => {
        state.aiLoading = false;
        state.aiAssistance = action.payload;
      })
      .addCase(generateAIIssueDescription.rejected, (state, action) => {
        state.aiLoading = false;
        state.error = action.payload as string;
      })
      // Add watcher reducers
      .addCase(addWatcher.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addWatcher.fulfilled, (state, action) => {
        state.loading = false;
        // The watcher will be updated when we refetch the issue
      })
      .addCase(addWatcher.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Remove watcher reducers
      .addCase(removeWatcher.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeWatcher.fulfilled, (state, action) => {
        state.loading = false;
        // The watcher will be updated when we refetch the issue
      })
      .addCase(removeWatcher.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  clearCurrentIssue,
  clearError,
  clearAIAssistance,
  startVoiceAssistance,
  stopVoiceAssistance,
  setVoiceListening,
  setVoiceTranscript,
  setVoiceStep,
  setVoiceConversationData,
  setVoiceError,
  clearVoiceAssistance
} = issueSlice.actions;
export default issueSlice.reducer;
